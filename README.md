# YApi 文生图接口对接

这是一个用Golang实现的文生图（Text-to-Image）API接口对接服务，支持多种文生图服务提供商。

## 功能特性

- 🎨 支持多种文生图服务（OpenAI DALL-E、Stable Diffusion、Midjourney等）
- 🚀 RESTful API接口
- ⚙️ 灵活的配置管理
- 📝 完善的日志记录
- 🔒 错误处理和重试机制
- 📊 请求监控和统计

## 支持的服务

- OpenAI DALL-E 2/3
- Stable Diffusion API
- Midjourney API
- 自定义API接口

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 配置

复制配置文件模板：

```bash
cp config/config.example.yaml config/config.yaml
```

编辑配置文件，填入相应的API密钥和配置。

### 运行

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

## API 接口

### 生成图片

```http
POST /api/v1/text-to-image
Content-Type: application/json

{
  "prompt": "a beautiful sunset over the ocean",
  "provider": "openai",
  "size": "1024x1024",
  "quality": "standard",
  "style": "vivid"
}
```

### 响应

```json
{
  "success": true,
  "data": {
    "images": [
      {
        "url": "https://example.com/image.png",
        "revised_prompt": "a beautiful sunset over the ocean with vibrant colors"
      }
    ],
    "provider": "openai",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

## 项目结构

```
.
├── cmd/                    # 应用程序入口
├── internal/              # 内部包
│   ├── api/              # API处理器
│   ├── client/           # 文生图客户端
│   ├── config/           # 配置管理
│   ├── models/           # 数据模型
│   └── utils/            # 工具函数
├── config/               # 配置文件
├── docs/                 # 文档
├── examples/             # 示例代码
└── tests/                # 测试文件
```

## 许可证

MIT License
