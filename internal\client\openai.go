package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
	"yapi-text-to-image/internal/models"
)

// OpenAIClient OpenAI DALL-E客户端
type OpenAIClient struct {
	*BaseClient
	httpClient *http.Client
}

// OpenAIRequest OpenAI请求结构
type OpenAIRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	N              int    `json:"n,omitempty"`
	Size           string `json:"size,omitempty"`
	Quality        string `json:"quality,omitempty"`
	Style          string `json:"style,omitempty"`
	ResponseFormat string `json:"response_format,omitempty"`
	User           string `json:"user,omitempty"`
}

// OpenAIResponse OpenAI响应结构
type OpenAIResponse struct {
	Created int64 `json:"created"`
	Data    []struct {
		URL           string `json:"url,omitempty"`
		B64JSON       string `json:"b64_json,omitempty"`
		RevisedPrompt string `json:"revised_prompt,omitempty"`
	} `json:"data"`
}

// OpenAIError OpenAI错误结构
type OpenAIError struct {
	Error struct {
		Code    string `json:"code"`
		Message string `json:"message"`
		Param   string `json:"param"`
		Type    string `json:"type"`
	} `json:"error"`
}

// NewOpenAIClient 创建OpenAI客户端
func NewOpenAIClient(config *ClientConfig) (TextToImageClient, error) {
	if config.APIKey == "" {
		return nil, models.NewAPIError(models.ErrCodeAPIKeyMissing, "OpenAI API key is required")
	}
	
	if config.BaseURL == "" {
		config.BaseURL = "https://api.openai.com/v1"
	}
	
	if config.Model == "" {
		config.Model = "dall-e-3"
	}
	
	timeout := time.Duration(config.Timeout) * time.Second
	if timeout == 0 {
		timeout = 60 * time.Second
	}
	
	return &OpenAIClient{
		BaseClient: NewBaseClient("openai", config),
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}, nil
}

// GenerateImage 生成图片
func (c *OpenAIClient) GenerateImage(ctx context.Context, req *models.TextToImageRequest) (*models.TextToImageData, error) {
	if err := c.ValidateRequest(req); err != nil {
		return nil, err
	}
	
	openaiReq := &OpenAIRequest{
		Model:          c.config.Model,
		Prompt:         req.Prompt,
		N:              req.N,
		Size:           req.Size,
		Quality:        req.Quality,
		Style:          req.Style,
		ResponseFormat: "url",
		User:           req.UserID,
	}
	
	// 设置默认值
	if openaiReq.N == 0 {
		openaiReq.N = 1
	}
	if openaiReq.Size == "" {
		openaiReq.Size = "1024x1024"
	}
	if openaiReq.Quality == "" {
		openaiReq.Quality = "standard"
	}
	
	reqBody, err := json.Marshal(openaiReq)
	if err != nil {
		return nil, models.NewAPIError(models.ErrCodeInternalError, "failed to marshal request")
	}
	
	httpReq, err := http.NewRequestWithContext(ctx, "POST", c.config.BaseURL+"/images/generations", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, models.NewAPIError(models.ErrCodeInternalError, "failed to create request")
	}
	
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.config.APIKey)
	
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, models.NewProviderError("openai", models.ErrCodeNetworkError, "network error: "+err.Error(), 0)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, models.NewProviderError("openai", models.ErrCodeNetworkError, "failed to read response", resp.StatusCode)
	}
	
	if resp.StatusCode != http.StatusOK {
		var openaiErr OpenAIError
		if err := json.Unmarshal(body, &openaiErr); err == nil {
			return nil, models.NewProviderError("openai", openaiErr.Error.Code, openaiErr.Error.Message, resp.StatusCode)
		}
		return nil, models.NewProviderError("openai", models.ErrCodeUnknown, string(body), resp.StatusCode)
	}
	
	var openaiResp OpenAIResponse
	if err := json.Unmarshal(body, &openaiResp); err != nil {
		return nil, models.NewProviderError("openai", models.ErrCodeInternalError, "failed to parse response", resp.StatusCode)
	}
	
	images := make([]models.GeneratedImage, len(openaiResp.Data))
	for i, img := range openaiResp.Data {
		images[i] = models.GeneratedImage{
			URL:           img.URL,
			B64JSON:       img.B64JSON,
			RevisedPrompt: img.RevisedPrompt,
		}
	}
	
	return &models.TextToImageData{
		Images:    images,
		Provider:  "openai",
		Model:     c.config.Model,
		CreatedAt: time.Unix(openaiResp.Created, 0),
		RequestID: req.RequestID,
	}, nil
}

// IsAvailable 检查服务是否可用
func (c *OpenAIClient) IsAvailable(ctx context.Context) bool {
	req, err := http.NewRequestWithContext(ctx, "GET", c.config.BaseURL+"/models", nil)
	if err != nil {
		return false
	}
	
	req.Header.Set("Authorization", "Bearer "+c.config.APIKey)
	
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == http.StatusOK
}

// GetSupportedSizes 获取支持的图片尺寸
func (c *OpenAIClient) GetSupportedSizes() []string {
	if c.config.Model == "dall-e-3" {
		return []string{"1024x1024", "1792x1024", "1024x1792"}
	}
	return []string{"256x256", "512x512", "1024x1024"}
}

// GetSupportedModels 获取支持的模型
func (c *OpenAIClient) GetSupportedModels() []string {
	return []string{"dall-e-2", "dall-e-3"}
}

// ValidateRequest 验证请求参数
func (c *OpenAIClient) ValidateRequest(req *models.TextToImageRequest) error {
	if req.Prompt == "" {
		return models.NewAPIError(models.ErrCodeInvalidPrompt, "prompt is required")
	}
	
	if req.N > 10 {
		return models.NewAPIError(models.ErrCodeInvalidRequest, "maximum 10 images per request")
	}
	
	// DALL-E 3 只支持生成1张图片
	if c.config.Model == "dall-e-3" && req.N > 1 {
		return models.NewAPIError(models.ErrCodeInvalidRequest, "DALL-E 3 only supports generating 1 image at a time")
	}
	
	// 验证尺寸
	if req.Size != "" {
		supportedSizes := c.GetSupportedSizes()
		valid := false
		for _, size := range supportedSizes {
			if req.Size == size {
				valid = true
				break
			}
		}
		if !valid {
			return models.NewAPIError(models.ErrCodeInvalidRequest, fmt.Sprintf("unsupported size: %s", req.Size))
		}
	}
	
	return nil
}
