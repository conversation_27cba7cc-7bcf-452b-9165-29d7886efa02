package models

import (
	"time"
)

// TextToImageResponse 文生图响应结构
type TextToImageResponse struct {
	Success bool                   `json:"success"`
	Data    *TextToImageData       `json:"data,omitempty"`
	Error   *ErrorResponse         `json:"error,omitempty"`
}

// TextToImageData 文生图数据
type TextToImageData struct {
	Images    []GeneratedImage `json:"images"`
	Provider  string           `json:"provider"`
	Model     string           `json:"model,omitempty"`
	CreatedAt time.Time        `json:"created_at"`
	RequestID string           `json:"request_id,omitempty"`
	Usage     *UsageInfo       `json:"usage,omitempty"`
}

// GeneratedImage 生成的图片信息
type GeneratedImage struct {
	URL            string `json:"url"`
	B64JSON        string `json:"b64_json,omitempty"`
	RevisedPrompt  string `json:"revised_prompt,omitempty"`
	Width          int    `json:"width,omitempty"`
	Height         int    `json:"height,omitempty"`
	Seed           int64  `json:"seed,omitempty"`
	FinishReason   string `json:"finish_reason,omitempty"`
}

// UsageInfo 使用信息
type UsageInfo struct {
	TotalTokens  int     `json:"total_tokens,omitempty"`
	PromptTokens int     `json:"prompt_tokens,omitempty"`
	Cost         float64 `json:"cost,omitempty"`
	Currency     string  `json:"currency,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// ProviderStatus 服务提供商状态
type ProviderStatus struct {
	Name      string    `json:"name"`
	Available bool      `json:"available"`
	LastCheck time.Time `json:"last_check"`
	Latency   int64     `json:"latency_ms"`
	Error     string    `json:"error,omitempty"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string           `json:"status"`
	Timestamp time.Time        `json:"timestamp"`
	Providers []ProviderStatus `json:"providers"`
	Version   string           `json:"version"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data *TextToImageData) *TextToImageResponse {
	return &TextToImageResponse{
		Success: true,
		Data:    data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code, message, details string) *TextToImageResponse {
	return &TextToImageResponse{
		Success: false,
		Error: &ErrorResponse{
			Code:    code,
			Message: message,
			Details: details,
		},
	}
}
