package models

import (
	"time"
)

// TextToImageRequest 文生图请求结构
type TextToImageRequest struct {
	// 必填字段
	Prompt   string `json:"prompt" binding:"required" example:"a beautiful sunset over the ocean"`
	Provider string `json:"provider" binding:"required" example:"openai"`

	// 可选字段
	Size     string `json:"size,omitempty" example:"1024x1024"`
	Quality  string `json:"quality,omitempty" example:"standard"`
	Style    string `json:"style,omitempty" example:"vivid"`
	N        int    `json:"n,omitempty" example:"1"`
	Model    string `json:"model,omitempty" example:"dall-e-3"`
	
	// 高级参数
	NegativePrompt string  `json:"negative_prompt,omitempty" example:"blurry, low quality"`
	Steps          int     `json:"steps,omitempty" example:"20"`
	CfgScale       float64 `json:"cfg_scale,omitempty" example:"7.5"`
	Seed           int64   `json:"seed,omitempty" example:"12345"`
	
	// 元数据
	UserID    string            `json:"user_id,omitempty"`
	RequestID string            `json:"request_id,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
}

// Validate 验证请求参数
func (r *TextToImageRequest) Validate() error {
	if r.Prompt == "" {
		return ErrInvalidPrompt
	}
	if r.Provider == "" {
		return ErrInvalidProvider
	}
	if r.N <= 0 {
		r.N = 1
	}
	if r.N > 10 {
		return ErrTooManyImages
	}
	return nil
}

// SetDefaults 设置默认值
func (r *TextToImageRequest) SetDefaults() {
	if r.Size == "" {
		r.Size = "1024x1024"
	}
	if r.Quality == "" {
		r.Quality = "standard"
	}
	if r.N <= 0 {
		r.N = 1
	}
	if r.Steps <= 0 {
		r.Steps = 20
	}
	if r.CfgScale <= 0 {
		r.CfgScale = 7.5
	}
}
