package client

import (
	"context"
	"yapi-text-to-image/internal/models"
)

// TextToImageClient 文生图客户端接口
type TextToImageClient interface {
	// GenerateImage 生成图片
	GenerateImage(ctx context.Context, req *models.TextToImageRequest) (*models.TextToImageData, error)
	
	// GetProviderName 获取服务提供商名称
	GetProviderName() string
	
	// IsAvailable 检查服务是否可用
	IsAvailable(ctx context.Context) bool
	
	// GetSupportedSizes 获取支持的图片尺寸
	GetSupportedSizes() []string
	
	// GetSupportedModels 获取支持的模型
	GetSupportedModels() []string
	
	// ValidateRequest 验证请求参数
	ValidateRequest(req *models.TextToImageRequest) error
}

// ClientConfig 客户端配置
type ClientConfig struct {
	APIKey     string            `json:"api_key" yaml:"api_key"`
	BaseURL    string            `json:"base_url" yaml:"base_url"`
	Model      string            `json:"model" yaml:"model"`
	Timeout    int               `json:"timeout" yaml:"timeout"`
	MaxRetries int               `json:"max_retries" yaml:"max_retries"`
	Extra      map[string]string `json:"extra" yaml:"extra"`
}

// ClientFactory 客户端工厂接口
type ClientFactory interface {
	CreateClient(provider string, config *ClientConfig) (TextToImageClient, error)
	GetSupportedProviders() []string
}

// DefaultClientFactory 默认客户端工厂
type DefaultClientFactory struct {
	clients map[string]func(*ClientConfig) (TextToImageClient, error)
}

// NewDefaultClientFactory 创建默认客户端工厂
func NewDefaultClientFactory() *DefaultClientFactory {
	factory := &DefaultClientFactory{
		clients: make(map[string]func(*ClientConfig) (TextToImageClient, error)),
	}
	
	// 注册支持的客户端
	factory.RegisterClient("openai", NewOpenAIClient)
	factory.RegisterClient("stable-diffusion", NewStableDiffusionClient)
	factory.RegisterClient("midjourney", NewMidjourneyClient)
	
	return factory
}

// RegisterClient 注册客户端
func (f *DefaultClientFactory) RegisterClient(provider string, creator func(*ClientConfig) (TextToImageClient, error)) {
	f.clients[provider] = creator
}

// CreateClient 创建客户端
func (f *DefaultClientFactory) CreateClient(provider string, config *ClientConfig) (TextToImageClient, error) {
	creator, exists := f.clients[provider]
	if !exists {
		return nil, models.NewAPIError(models.ErrCodeProviderNotFound, "unsupported provider: "+provider)
	}
	
	return creator(config)
}

// GetSupportedProviders 获取支持的服务提供商
func (f *DefaultClientFactory) GetSupportedProviders() []string {
	providers := make([]string, 0, len(f.clients))
	for provider := range f.clients {
		providers = append(providers, provider)
	}
	return providers
}

// BaseClient 基础客户端结构
type BaseClient struct {
	config   *ClientConfig
	provider string
}

// NewBaseClient 创建基础客户端
func NewBaseClient(provider string, config *ClientConfig) *BaseClient {
	return &BaseClient{
		config:   config,
		provider: provider,
	}
}

// GetProviderName 获取服务提供商名称
func (c *BaseClient) GetProviderName() string {
	return c.provider
}

// GetConfig 获取配置
func (c *BaseClient) GetConfig() *ClientConfig {
	return c.config
}
