package models

import (
	"errors"
	"fmt"
)

// 预定义错误
var (
	ErrInvalidPrompt    = errors.New("invalid prompt: prompt cannot be empty")
	ErrInvalidProvider  = errors.New("invalid provider: provider cannot be empty")
	ErrTooManyImages    = errors.New("too many images: maximum 10 images per request")
	ErrProviderNotFound = errors.New("provider not found")
	ErrAPIKeyMissing    = errors.New("API key is missing")
	ErrRateLimitExceeded = errors.New("rate limit exceeded")
	ErrInsufficientCredits = errors.New("insufficient credits")
	ErrContentFiltered  = errors.New("content filtered by safety system")
	ErrInternalError    = errors.New("internal server error")
	ErrTimeout          = errors.New("request timeout")
	ErrNetworkError     = errors.New("network error")
)

// APIError API错误结构
type APIError struct {
	Code       string `json:"code"`
	Message    string `json:"message"`
	Details    string `json:"details,omitempty"`
	StatusCode int    `json:"status_code,omitempty"`
	Provider   string `json:"provider,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	if e.Provider != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Provider, e.Code, e.Message)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewAPIError 创建API错误
func NewAPIError(code, message string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
	}
}

// NewAPIErrorWithDetails 创建带详情的API错误
func NewAPIErrorWithDetails(code, message, details string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewProviderError 创建服务提供商错误
func NewProviderError(provider, code, message string, statusCode int) *APIError {
	return &APIError{
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Provider:   provider,
	}
}

// 错误代码常量
const (
	ErrCodeInvalidRequest    = "INVALID_REQUEST"
	ErrCodeInvalidPrompt     = "INVALID_PROMPT"
	ErrCodeInvalidProvider   = "INVALID_PROVIDER"
	ErrCodeProviderNotFound  = "PROVIDER_NOT_FOUND"
	ErrCodeAPIKeyMissing     = "API_KEY_MISSING"
	ErrCodeRateLimitExceeded = "RATE_LIMIT_EXCEEDED"
	ErrCodeInsufficientCredits = "INSUFFICIENT_CREDITS"
	ErrCodeContentFiltered   = "CONTENT_FILTERED"
	ErrCodeInternalError     = "INTERNAL_ERROR"
	ErrCodeTimeout           = "TIMEOUT"
	ErrCodeNetworkError      = "NETWORK_ERROR"
	ErrCodeUnknown           = "UNKNOWN_ERROR"
)

// IsRetryableError 判断错误是否可重试
func IsRetryableError(err error) bool {
	if apiErr, ok := err.(*APIError); ok {
		switch apiErr.Code {
		case ErrCodeTimeout, ErrCodeNetworkError, ErrCodeInternalError:
			return true
		case ErrCodeRateLimitExceeded:
			return true // 可以延迟重试
		}
		// HTTP 5xx 错误通常可重试
		if apiErr.StatusCode >= 500 && apiErr.StatusCode < 600 {
			return true
		}
	}
	return false
}
